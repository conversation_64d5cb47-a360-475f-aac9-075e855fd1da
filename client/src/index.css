@import "tailwindcss";

/* Rich Text Editor styling */
.rich-text-editor [contenteditable="true"]:empty::before {
  content: attr(data-placeholder);
  color: #9ca3af;
  font-style: italic;
  pointer-events: none;
}

.rich-text-editor [contenteditable="true"] ul {
  list-style-type: disc;
  padding-left: 20px;
  margin: 0.5rem 0;
}

.rich-text-editor [contenteditable="true"] ol {
  list-style-type: decimal;
  padding-left: 20px;
  margin: 0.5rem 0;
}

.rich-text-editor [contenteditable="true"] li {
  margin: 0.25rem 0;
  line-height: 1.5;
}

.rich-text-editor [contenteditable="true"] h1 {
  font-size: 1.5em;
  font-weight: bold;
  margin: 0.5em 0;
}

.rich-text-editor [contenteditable="true"] h2 {
  font-size: 1.3em;
  font-weight: bold;
  margin: 0.5em 0;
}

/* Ensure nested lists work properly */
.rich-text-editor [contenteditable="true"] ul ul {
  list-style-type: circle;
  margin: 0.25rem 0;
}

.rich-text-editor [contenteditable="true"] ul ul ul {
  list-style-type: square;
  margin: 0.25rem 0;
}

.rich-text-editor [contenteditable="true"] ol ol {
  list-style-type: lower-alpha;
  margin: 0.25rem 0;
}

.rich-text-editor [contenteditable="true"] ol ol ol {
  list-style-type: lower-roman;
  margin: 0.25rem 0;
}

/* Smooth scrolling for all scrollable elements */
* {
  scroll-behavior: smooth;
}

/* Ensure proper scrolling in preview containers */
.preview-container {
  scroll-behavior: smooth;
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
}

.preview-container::-webkit-scrollbar {
  width: 6px;
}

.preview-container::-webkit-scrollbar-track {
  background: transparent;
}

.preview-container::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.5);
  border-radius: 3px;
}

.preview-container::-webkit-scrollbar-thumb:hover {
  background-color: rgba(156, 163, 175, 0.7);
}


/* Preview styling (without !important as these shouldn't conflict) */
.prose ul {
  list-style-type: disc;
  padding-left: 1.5rem;
  margin: 0.5rem 0;
}

.prose ol {
  list-style-type: decimal;
  padding-left: 1.5rem;
  margin: 0.5rem 0;
}

.prose li {
  margin: 0.25rem 0;
  line-height: 1.5;
}

.prose ul ul {
  list-style-type: circle;
}

.prose ul ul ul {
  list-style-type: square;
}

/* Ensure list styles are preserved in all preview contexts */
[dangerouslySetInnerHTML] ul {
  list-style-type: disc;
  padding-left: 1.5rem;
  margin: 0.5rem 0;
}

[dangerouslySetInnerHTML] ol {
  list-style-type: decimal;
  padding-left: 1.5rem;
  margin: 0.5rem 0;
}

[dangerouslySetInnerHTML] li {
  margin: 0.25rem 0;
  line-height: 1.5;
}

/* TinyMCE placeholder styling handled in component */
