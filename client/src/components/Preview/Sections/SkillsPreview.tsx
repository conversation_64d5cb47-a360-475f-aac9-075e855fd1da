import React from 'react';
import { Skill, SectionSettings } from '../../../types/cv.types';

interface SkillsPreviewProps {
  skills: Skill[];
  sectionSettings?: SectionSettings;
}

const SkillsPreview: React.FC<SkillsPreviewProps> = ({ skills, sectionSettings }) => {
  if (!skills.length) return null;

  const showProficiencyLevels = sectionSettings?.skills?.showProficiencyLevels ?? false;

  // Group skills by category
  const groupedSkills = skills.reduce((acc, skill) => {
    const category = skill.category || 'Uncategorized';
    if (!acc[category]) {
      acc[category] = [];
    }
    acc[category].push(skill);
    return acc;
  }, {} as Record<string, Skill[]>);

  // Sort skills within each category by level if proficiency levels are shown
  Object.keys(groupedSkills).forEach(category => {
    if (showProficiencyLevels) {
      groupedSkills[category].sort((a, b) => (b.level || 0) - (a.level || 0));
    }
  });

  return (
    <div className="mb-6">
      <h2 className="text-lg font-bold text-gray-800 border-b border-gray-200 pb-1 mb-3">
        Skills
      </h2>

      <div className="space-y-4">
        {Object.entries(groupedSkills).map(([category, categorySkills]) => (
          <div key={category}>
            <h3 className="text-md font-semibold text-gray-700 mb-2">{category}</h3>
            <div className="flex flex-wrap gap-2">
              {categorySkills.map((skill) => (
                <span 
                  key={skill.id}
                  className="px-2 py-0.5 bg-gray-100 text-gray-700 text-xs rounded-full flex items-center gap-1"
                >
                  {skill.name}
                  {showProficiencyLevels && skill.level && (
                    <span className="text-blue-600 font-medium">
                      {skill.level}/5
                    </span>
                  )}
                </span>
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default SkillsPreview;