import puppeteer, { <PERSON><PERSON><PERSON> } from 'puppeteer';
import { CVData, SectionVisibility } from '../types/cv.ts';
import { HtmlGenerator } from '../utils/htmlGenerator.ts';
import { createAppLogger } from '../utils/logger.ts';
import { BadRequestError, InternalServerError } from '../utils/errors.ts';

/**
 * PDF Service class following Single Responsibility Principle
 * Handles all PDF generation functionalities
 */
export class PdfService {
  private htmlGenerator: HtmlGenerator;
  private logger: ReturnType<typeof createAppLogger>;

  constructor() {
    this.htmlGenerator = new HtmlGenerator();
    this.logger = createAppLogger();
  }

  /**
   * Generate a CV PDF from data using Puppeteer
   * @param data - CV data
   * @param visibility - Visibility settings for sections
   * @param templateName - Template name to use (default: 'classic')
   * @returns PDF buffer as Uint8Array
   */
  public async generatePdf(data: CVData | null, visibility: SectionVisibility | null, templateName = 'classic-0'): Promise<Uint8Array> {
    if (!data || !visibility) {
      throw new BadRequestError('CV data and visibility settings are required');
    }

    let browser: Browser | null = null;
    try {
      // Generate HTML from CV data using template
      const html = await this.htmlGenerator.generateHTML(data, visibility, templateName);

      // Launch a headless browser with recommended security settings
      browser = await puppeteer.launch({
        headless: true,
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-accelerated-2d-canvas',
          '--disable-gpu',
        ],
      });

      // Create a new page
      const page = await browser.newPage();

      // Set the page content to our HTML
      await page.setContent(html, {
        waitUntil: 'networkidle0', // Wait until all resources are loaded
      });

      // Wait for fonts to load and content to render properly
      await page.evaluate(() => new Promise(resolve => setTimeout(resolve, 1000)));
      
      // Wait for any images to load
      await page.evaluate(() => {
        return Promise.all(Array.from(document.images, img => {
          if (img.complete) return Promise.resolve();
          return new Promise(resolve => {
            img.addEventListener('load', resolve);
            img.addEventListener('error', resolve);
          });
        }));
      });

      // Generate a PDF with optimized settings for consistency
      const pdfBuffer = await page.pdf({
        format: 'A4',
        printBackground: true,
        margin: {
          top: '0.5in',
          right: '0.5in',
          bottom: '0.5in',
          left: '0.5in',
        },
        displayHeaderFooter: false,
        preferCSSPageSize: false,
        scale: 0.8, // Slightly scale down to ensure content fits well
      });

      return pdfBuffer;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error('Error generating PDF', { error: errorMessage, stack: error instanceof Error ? error.stack : undefined });
      // Throw a specific AppError for better handling downstream
      throw new InternalServerError(`Failed to generate PDF: ${errorMessage}`);
    } finally {
      if (browser) {
        await browser.close();
      }
    }
  }

  /**
   * Get suggested filename for the generated PDF
   * @param data - CV data
   * @returns Suggested filename
   */
  public getSuggestedFilename(data: CVData | null): string {
    if (!data?.personalInfo) {
      return 'resume.pdf';
    }
    const { firstName, lastName } = data.personalInfo;
    const nameParts: string[] = [];
    if (firstName && firstName.trim()) {
      nameParts.push(firstName.trim());
    }
    if (lastName && lastName.trim()) {
      nameParts.push(lastName.trim());
    }

    if (nameParts.length === 0) {
      return 'resume.pdf';
    }

    return `${nameParts.join('_')}_Resume.pdf`;
  }

  /**
   * Get list of available templates
   * @returns Array of template names
   */
  public async getAvailableTemplates(): Promise<string[]> {
    return await this.htmlGenerator.getAvailableTemplates();
  }

  /**
   * Generate HTML from CV data (for preview)
   * @param data - CV data
   * @param visibility - Visibility settings for sections
   * @param templateName - Template name to use (default: 'classic')
   * @returns HTML string
   */
  public async generateHTML(data: CVData, visibility: SectionVisibility, templateName = 'classic-0'): Promise<string> {
    return await this.htmlGenerator.generateHTML(data, visibility, templateName);
  }

  /**
   * Generate PDF from pre-rendered HTML content (unified approach for all templates)
   * @param htmlContent - Pre-rendered HTML content
   * @param styles - CSS styles to include
   * @returns PDF buffer as Uint8Array
   */
  public async generatePdfFromHtml(htmlContent: string, styles: string = ''): Promise<Uint8Array> {
    console.log('🚀 PdfService.generatePdfFromHtml called');
    console.log('- HTML length:', htmlContent?.length || 0);
    console.log('- Styles length:', styles?.length || 0);
    
    // Debug: Show first 500 chars of HTML to see what's being captured
    console.log('🔍 HTML Preview (first 500 chars):');
    console.log(htmlContent.substring(0, 500));
    
    // Debug: Check if HTML contains key elements
    console.log('🔍 HTML Analysis:');
    console.log('- Contains cv-preview:', htmlContent.includes('cv-preview'));
    console.log('- Contains gradient classes:', htmlContent.includes('bg-gradient'));
    console.log('- Contains text content:', htmlContent.includes('Damilola') || htmlContent.includes('Michael'));
    
    // Debug: Show first 1000 chars of styles
    console.log('🔍 Styles Preview (first 1000 chars):');
    console.log(styles.substring(0, 1000));
    
    let browser: Browser | null = null;
    try {
      // Create a complete HTML document with minimal interference to preserve preview styling
      const fullHtml = `
        <!DOCTYPE html>
        <html lang="en">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>CV</title>
          <link rel="preconnect" href="https://fonts.googleapis.com">
          <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
          <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Lora:wght@700&family=Plus+Jakarta+Sans:wght@400;500;600;700&display=swap" rel="stylesheet">
          <script src="https://cdn.tailwindcss.com"></script>
          <style>
            /* Include extracted styles from preview */
            ${styles}
            
            /* Minimal PDF-specific overrides - preserve preview appearance exactly */
            * {
              -webkit-print-color-adjust: exact !important;
              print-color-adjust: exact !important;
              box-sizing: border-box !important;
            }
            
            html, body { 
              margin: 0 !important; 
              padding: 0 !important; 
              font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif !important;
              height: 100% !important;
              min-height: 100vh !important;
            }
            
            /* Ensure the page background covers the entire page dynamically */
            html, body {
              min-height: 100vh !important;
            }
            
            /* Let the outermost container determine the page background */
            body > div:first-child {
              min-height: 100vh !important;
            }
            
            /* Reduce template-specific padding for PDFs */
            .cv-content,
            .max-w-4xl {
              margin: 0 !important;
              padding: 0.5rem !important;
            }
            
            /* Reduce padding classes for more compact PDF layout */
            .p-6 { padding: 0.75rem !important; }
            .p-8 { padding: 1rem !important; }
            .bg-gray-50,
            .bg-gray-900 {
              padding: 0.5rem !important;
            }
            
            /* Ensure layouts use full available width for multi-column designs */
            .max-w-4xl {
              max-width: none !important;
              width: 100% !important;
            }
            
            /* Force grid layouts to maintain their column structure */
            .grid {
              display: grid !important;
            }
            
            .lg\\:grid-cols-3 {
              grid-template-columns: repeat(3, minmax(0, 1fr)) !important;
            }
            
            /* Remove only interactive elements - preserve all visual styling */
            button, 
            [role="button"] { 
              display: none !important; 
            }
            
            /* Hide hover pseudo-classes but keep base styles */
            *:hover {
              /* Reset hover effects to base state */
            }
            
            /* Ensure all backgrounds and colors render exactly as in preview */
            * {
              -webkit-print-color-adjust: exact !important;
              print-color-adjust: exact !important;
            }
            
            /* Page break control */
            .page-break { page-break-before: always !important; }
            .no-break { page-break-inside: avoid !important; }
          </style>
        </head>
        <body>
          ${htmlContent}
        </body>
        </html>
      `;

      // Launch a headless browser with recommended security settings
      browser = await puppeteer.launch({
        headless: true,
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-accelerated-2d-canvas',
          '--disable-gpu',
        ],
      });

      // Create a new page
      const page = await browser.newPage();

      // Set viewport to desktop size to trigger responsive breakpoints
      await page.setViewport({
        width: 1200,  // Wide enough to trigger lg: breakpoints (1024px+)
        height: 1600, // Tall enough for full content
        deviceScaleFactor: 1,
      });

      // Set the page content to our HTML
      await page.setContent(fullHtml, {
        waitUntil: 'networkidle0', // Wait until all resources are loaded
      });

      // Wait for fonts to load and content to render properly
      await page.evaluate(() => new Promise(resolve => setTimeout(resolve, 1500)));
      
      // Dynamically set body background to match the outermost container
      await page.evaluate(() => {
        const outerContainer = document.body.firstElementChild as HTMLElement;
        if (outerContainer) {
          const computedStyle = getComputedStyle(outerContainer);
          const backgroundColor = computedStyle.backgroundColor;
          
          // Only set if the container has a non-transparent background
          if (backgroundColor && backgroundColor !== 'rgba(0, 0, 0, 0)' && backgroundColor !== 'transparent') {
            document.body.style.backgroundColor = backgroundColor;
            console.log('🎨 Dynamic background set:', backgroundColor);
          } else {
            // Fallback: look for any element with a background color
            const allElements = document.querySelectorAll('*');
            for (const element of allElements) {
              const style = getComputedStyle(element);
              const bgColor = style.backgroundColor;
              if (bgColor && bgColor !== 'rgba(0, 0, 0, 0)' && bgColor !== 'transparent') {
                document.body.style.backgroundColor = bgColor;
                console.log('🎨 Fallback background set:', bgColor);
                break;
              }
            }
          }
        }
      });
      
      // Debug: Check viewport and responsive classes
      const debugInfo = await page.evaluate(() => {
        return {
          viewport: {
            width: window.innerWidth,
            height: window.innerHeight
          },
          gridElements: Array.from(document.querySelectorAll('[class*="grid-cols"]')).map(el => ({
            classes: el.className,
            computedGrid: getComputedStyle(el).gridTemplateColumns
          }))
        };
      });
      
      console.log('🔍 Viewport & Layout Debug:');
      console.log('- Viewport:', debugInfo.viewport);
      console.log('- Grid elements:', debugInfo.gridElements);
      
      // Additional layout debugging
      const layoutInfo = await page.evaluate(() => {
        const gridElement = document.querySelector('.grid.grid-cols-1.lg\\:grid-cols-3');
        const maxWidthElement = document.querySelector('.max-w-4xl');
        return {
          gridElement: gridElement ? {
            width: gridElement.offsetWidth,
            computedDisplay: getComputedStyle(gridElement).display,
            computedGridCols: getComputedStyle(gridElement).gridTemplateColumns,
            clientWidth: gridElement.clientWidth
          } : null,
          maxWidthElement: maxWidthElement ? {
            width: maxWidthElement.offsetWidth,
            computedMaxWidth: getComputedStyle(maxWidthElement).maxWidth,
            computedWidth: getComputedStyle(maxWidthElement).width
          } : null,
          bodyWidth: document.body.offsetWidth,
          documentWidth: document.documentElement.offsetWidth
        };
      });
      
      console.log('🔍 Layout Details:');
      console.log('- Body width:', layoutInfo.bodyWidth);
      console.log('- Document width:', layoutInfo.documentWidth);
      console.log('- Grid element:', layoutInfo.gridElement);
      console.log('- Max-width element:', layoutInfo.maxWidthElement);
      
      // Wait for any images to load
      await page.evaluate(() => {
        return Promise.all(Array.from(document.images, img => {
          if (img.complete) return Promise.resolve();
          return new Promise(resolve => {
            img.addEventListener('load', resolve);
            img.addEventListener('error', resolve);
          });
        }));
      });

      // Generate a PDF with settings optimized for layout preservation
      const pdfBuffer = await page.pdf({
        format: 'A4',
        printBackground: true,
        margin: {
          top: '0',
          right: '0', 
          bottom: '0',
          left: '0',
        },
        displayHeaderFooter: false,
        preferCSSPageSize: false,
        scale: 0.75, // Scale down slightly to ensure multi-column layouts fit on A4
      });

      return pdfBuffer;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error('Error generating PDF from HTML', { error: errorMessage, stack: error instanceof Error ? error.stack : undefined });
      // Throw a specific AppError for better handling downstream
      throw new InternalServerError(`Failed to generate PDF from HTML: ${errorMessage}`);
    } finally {
      if (browser) {
        await browser.close();
      }
    }
  }
}
